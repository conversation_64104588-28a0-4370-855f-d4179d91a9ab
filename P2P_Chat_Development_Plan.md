# P2P聊天应用开发计划

## 项目概述

**项目名称**: SecureChat P2P  
**技术栈**: Tauri + Rust + React + TypeScript + TailwindCSS  
**目标平台**: Windows 桌面应用  
**开发周期**: 8-10周  

## 技术架构

### 前端架构 (React + TypeScript)
```
src/
├── components/          # React组件
│   ├── Chat/           # 聊天相关组件
│   ├── ContactList/    # 联系人列表
│   ├── Discovery/      # 用户发现
│   └── Settings/       # 设置界面
├── hooks/              # 自定义Hooks
├── store/              # 状态管理 (Zustand)
├── types/              # TypeScript类型定义
├── utils/              # 工具函数
└── styles/             # TailwindCSS样式
```

### 后端架构 (Rust)
```
src-tauri/src/
├── main.rs             # 主入口
├── commands/           # Tauri命令
├── network/            # 网络通信模块
│   ├── p2p.rs         # P2P连接管理
│   ├── discovery.rs   # 局域网发现
│   └── server.rs      # 发现服务器通信
├── crypto/             # 加密模块
├── storage/            # 数据存储
└── models/             # 数据模型
```

## 核心技术选型

### Rust依赖库
```toml
[dependencies]
tauri = { version = "1.0", features = ["api-all"] }
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
sqlx = { version = "0.7", features = ["sqlite", "runtime-tokio-rustls"] }
ring = "0.16"           # 加密库
uuid = "1.0"            # UUID生成
mdns = "3.0"            # mDNS服务发现
```

### 前端依赖
```json
{
  "dependencies": {
    "react": "^18.2.0",
    "typescript": "^5.0.0",
    "@tauri-apps/api": "^1.5.0",
    "zustand": "^4.4.0",
    "react-router-dom": "^6.15.0",
    "lucide-react": "^0.263.0",
    "tailwindcss": "^3.3.0"
  }
}
```

## 阶段性开发计划

## 第一阶段：基础框架搭建 (1-2周)

### 目标
建立完整的开发环境和基础UI框架

### 具体任务

#### 1.1 项目初始化
- [ ] 创建Tauri项目
- [ ] 配置React + TypeScript + TailwindCSS
- [ ] 设置开发环境和构建脚本
- [ ] 配置ESLint、Prettier代码规范

#### 1.2 基础UI组件
- [ ] 主窗口布局设计
- [ ] 联系人列表组件
- [ ] 聊天窗口组件
- [ ] 基础样式系统

#### 1.3 状态管理
- [ ] Zustand store配置
- [ ] 用户状态管理
- [ ] 消息状态管理
- [ ] UI状态管理

#### 1.4 Tauri后端基础
- [ ] 基础命令结构
- [ ] 错误处理机制
- [ ] 日志系统
- [ ] 配置文件管理

### 可测试功能
- 应用启动和基础界面显示
- 组件间导航
- 基础状态管理功能

### 验收标准
- [ ] 应用能正常启动
- [ ] 界面布局完整美观
- [ ] 前后端通信正常
- [ ] 代码规范检查通过

## 第二阶段：本地数据存储 (1-2周)

### 目标
实现消息和用户数据的本地存储功能

### 具体任务

#### 2.1 数据库设计
- [ ] SQLite数据库初始化
- [ ] 用户表结构设计
- [ ] 消息表结构设计
- [ ] 会话表结构设计

#### 2.2 数据访问层
- [ ] 数据库连接管理
- [ ] 用户CRUD操作
- [ ] 消息CRUD操作
- [ ] 数据迁移机制

#### 2.3 前端数据接口
- [ ] Tauri命令定义
- [ ] 前端API封装
- [ ] 数据缓存策略
- [ ] 错误处理

#### 2.4 基础聊天功能
- [ ] 消息发送界面
- [ ] 消息显示组件
- [ ] 本地消息存储
- [ ] 聊天历史查看

### 可测试功能
- 创建和管理联系人
- 发送和接收本地消息
- 查看聊天历史
- 数据持久化

### 验收标准
- [ ] 数据库操作正常
- [ ] 消息能正确存储和读取
- [ ] 界面数据实时更新
- [ ] 应用重启后数据保持

## 第三阶段：局域网发现 (2周)

### 目标
实现局域网内用户自动发现和连接

### 具体任务

#### 3.1 网络发现模块
- [ ] UDP广播发现实现
- [ ] mDNS服务注册
- [ ] 网络接口检测
- [ ] 发现消息协议定义

#### 3.2 P2P连接基础
- [ ] TCP服务器启动
- [ ] 连接管理器
- [ ] 消息序列化/反序列化
- [ ] 连接状态跟踪

#### 3.3 用户发现界面
- [ ] 局域网用户列表
- [ ] 连接状态显示
- [ ] 手动连接功能
- [ ] 连接请求处理

#### 3.4 基础消息传输
- [ ] 点对点消息发送
- [ ] 消息确认机制
- [ ] 连接重试逻辑
- [ ] 网络错误处理

### 可测试功能
- 局域网用户自动发现
- 建立P2P连接
- 发送实时消息
- 连接状态管理

### 验收标准
- [ ] 能发现同网段用户
- [ ] P2P连接建立成功
- [ ] 消息实时传输
- [ ] 网络异常处理正确

## 第四阶段：加密安全 (2周)

### 目标
实现端到端加密和安全机制

### 具体任务

#### 4.1 密钥管理
- [ ] RSA密钥对生成
- [ ] 密钥安全存储
- [ ] 公钥交换机制
- [ ] 会话密钥协商

#### 4.2 消息加密
- [ ] AES消息加密
- [ ] 数字签名验证
- [ ] 加密消息格式
- [ ] 密钥轮换机制

#### 4.3 安全界面
- [ ] 加密状态指示
- [ ] 密钥验证界面
- [ ] 安全设置页面
- [ ] 警告提示机制

#### 4.4 数据库加密
- [ ] 本地数据加密存储
- [ ] 敏感信息保护
- [ ] 安全配置管理
- [ ] 数据完整性检查

### 可测试功能
- 加密消息传输
- 密钥管理
- 安全状态显示
- 数据安全存储

### 验收标准
- [ ] 消息端到端加密
- [ ] 密钥安全管理
- [ ] 加密状态可视化
- [ ] 本地数据安全

## 第五阶段：发现服务器集成 (2周)

### 目标
集成发现服务器，支持跨网络用户发现

### 具体任务

#### 5.1 服务器通信
- [ ] HTTP/WebSocket客户端
- [ ] 用户注册接口
- [ ] 在线状态同步
- [ ] 服务器重连机制

#### 5.2 NAT穿透
- [ ] STUN客户端实现
- [ ] 打洞协议
- [ ] 中继连接备选
- [ ] 网络类型检测

#### 5.3 用户管理
- [ ] 远程用户搜索
- [ ] 好友请求系统
- [ ] 用户状态同步
- [ ] 离线消息处理

#### 5.4 连接优化
- [ ] 连接质量检测
- [ ] 自动重连机制
- [ ] 多路径连接
- [ ] 负载均衡

### 可测试功能
- 跨网络用户发现
- NAT穿透连接
- 服务器辅助通信
- 离线消息同步

### 验收标准
- [ ] 服务器连接稳定
- [ ] NAT穿透成功率高
- [ ] 跨网络消息传输
- [ ] 离线消息正确处理

## 第六阶段：功能完善和优化 (1-2周)

### 目标
完善用户体验，优化性能和稳定性

### 具体任务

#### 6.1 用户体验优化
- [ ] 界面动画效果
- [ ] 消息状态指示
- [ ] 通知系统
- [ ] 快捷键支持

#### 6.2 性能优化
- [ ] 消息加载优化
- [ ] 内存使用优化
- [ ] 网络连接优化
- [ ] 数据库查询优化

#### 6.3 错误处理
- [ ] 全局错误捕获
- [ ] 用户友好错误提示
- [ ] 日志记录完善
- [ ] 崩溃恢复机制

#### 6.4 测试和调试
- [ ] 单元测试编写
- [ ] 集成测试
- [ ] 性能测试
- [ ] 安全测试

### 可测试功能
- 完整的聊天体验
- 稳定的网络连接
- 优秀的用户界面
- 可靠的错误处理

### 验收标准
- [ ] 用户体验流畅
- [ ] 性能指标达标
- [ ] 错误处理完善
- [ ] 测试覆盖率充足

## 开发环境配置

### 必需工具
- Rust 1.70+
- Node.js 18+
- Tauri CLI
- Git

### 开发命令
```bash
# 安装依赖
npm install
cargo install tauri-cli

# 开发模式
npm run tauri dev

# 构建应用
npm run tauri build

# 运行测试
cargo test
npm test
```

## 项目里程碑

| 阶段 | 时间 | 主要功能 | 交付物 |
|------|------|----------|--------|
| 阶段1 | 第1-2周 | 基础框架 | 可运行的应用框架 |
| 阶段2 | 第3-4周 | 数据存储 | 本地聊天功能 |
| 阶段3 | 第5-6周 | 局域网发现 | P2P聊天功能 |
| 阶段4 | 第7-8周 | 安全加密 | 加密聊天功能 |
| 阶段5 | 第9-10周 | 服务器集成 | 完整P2P应用 |
| 阶段6 | 第11-12周 | 优化完善 | 发布版本 |

## 风险评估

### 技术风险
- NAT穿透成功率
- 加密性能影响
- 跨平台兼容性

### 缓解措施
- 提供中继服务器备选
- 优化加密算法选择
- 充分测试不同环境

## 详细技术实现

### 核心数据结构

#### Rust后端类型定义
```rust
// src-tauri/src/models/mod.rs
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct User {
    pub id: String,
    pub display_name: String,
    pub public_key: String,
    pub ip_address: Option<String>,
    pub port: Option<u16>,
    pub last_seen: chrono::DateTime<chrono::Utc>,
    pub is_online: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Message {
    pub id: String,
    pub from_user: String,
    pub to_user: String,
    pub content: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub message_type: MessageType,
    pub status: MessageStatus,
    pub encrypted: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MessageType {
    Text,
    File,
    System,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MessageStatus {
    Sending,
    Sent,
    Delivered,
    Read,
    Failed,
}
```

#### TypeScript前端类型定义
```typescript
// src/types/index.ts
export interface User {
  id: string;
  displayName: string;
  publicKey: string;
  ipAddress?: string;
  port?: number;
  lastSeen: string;
  isOnline: boolean;
}

export interface Message {
  id: string;
  fromUser: string;
  toUser: string;
  content: string;
  timestamp: string;
  messageType: 'text' | 'file' | 'system';
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  encrypted: boolean;
}

export interface Conversation {
  id: string;
  participants: [string, string];
  lastMessage?: Message;
  unreadCount: number;
  lastActivity: string;
}
```

### 网络协议设计

#### 消息协议格式
```rust
// src-tauri/src/network/protocol.rs
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkMessage {
    pub version: u8,
    pub message_type: NetworkMessageType,
    pub from: String,
    pub to: String,
    pub timestamp: i64,
    pub message_id: String,
    pub payload: Vec<u8>,
    pub signature: Option<Vec<u8>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum NetworkMessageType {
    Discover,
    DiscoverResponse,
    ConnectionRequest,
    ConnectionResponse,
    Message,
    Heartbeat,
    KeyExchange,
    Disconnect,
}
```

#### 局域网发现实现
```rust
// src-tauri/src/network/discovery.rs
use std::net::{UdpSocket, SocketAddr};
use tokio::time::{interval, Duration};

pub struct LanDiscovery {
    socket: UdpSocket,
    local_user: User,
    discovered_users: Arc<Mutex<HashMap<String, User>>>,
}

impl LanDiscovery {
    pub async fn start_discovery(&self) -> Result<(), Box<dyn std::error::Error>> {
        let mut interval = interval(Duration::from_secs(30));

        loop {
            interval.tick().await;
            self.broadcast_presence().await?;
        }
    }

    async fn broadcast_presence(&self) -> Result<(), Box<dyn std::error::Error>> {
        let discover_msg = NetworkMessage {
            version: 1,
            message_type: NetworkMessageType::Discover,
            from: self.local_user.id.clone(),
            to: "broadcast".to_string(),
            timestamp: chrono::Utc::now().timestamp(),
            message_id: Uuid::new_v4().to_string(),
            payload: serde_json::to_vec(&self.local_user)?,
            signature: None,
        };

        let broadcast_addr = "255.255.255.255:8888";
        let serialized = serde_json::to_vec(&discover_msg)?;
        self.socket.send_to(&serialized, broadcast_addr)?;

        Ok(())
    }
}
```

### 加密实现

#### 密钥管理
```rust
// src-tauri/src/crypto/keys.rs
use ring::{rand, signature, agreement};
use ring::signature::{RsaKeyPair, RSA_PKCS1_SHA256};

pub struct KeyManager {
    rsa_keypair: RsaKeyPair,
    public_key_der: Vec<u8>,
}

impl KeyManager {
    pub fn new() -> Result<Self, Box<dyn std::error::Error>> {
        let rng = rand::SystemRandom::new();
        let pkcs8_bytes = signature::RsaKeyPair::generate_pkcs8(&rng, 2048)?;
        let keypair = signature::RsaKeyPair::from_pkcs8(pkcs8_bytes.as_ref())?;
        let public_key_der = keypair.public_key().as_ref().to_vec();

        Ok(KeyManager {
            rsa_keypair: keypair,
            public_key_der,
        })
    }

    pub fn encrypt_message(&self, message: &str, recipient_public_key: &[u8]) -> Result<Vec<u8>, Box<dyn std::error::Error>> {
        // 实现AES加密逻辑
        // 使用RSA加密AES密钥
        // 返回加密后的消息
        todo!()
    }

    pub fn decrypt_message(&self, encrypted_data: &[u8]) -> Result<String, Box<dyn std::error::Error>> {
        // 实现AES解密逻辑
        // 使用RSA解密AES密钥
        // 返回解密后的消息
        todo!()
    }
}
```

### 前端状态管理

#### Zustand Store设计
```typescript
// src/store/chatStore.ts
import { create } from 'zustand';
import { User, Message, Conversation } from '../types';

interface ChatState {
  // 状态
  currentUser: User | null;
  users: User[];
  conversations: Conversation[];
  messages: Record<string, Message[]>;
  activeConversation: string | null;

  // 动作
  setCurrentUser: (user: User) => void;
  addUser: (user: User) => void;
  updateUser: (userId: string, updates: Partial<User>) => void;
  removeUser: (userId: string) => void;

  addMessage: (message: Message) => void;
  updateMessage: (messageId: string, updates: Partial<Message>) => void;

  setActiveConversation: (conversationId: string) => void;
  createConversation: (participants: [string, string]) => void;
}

export const useChatStore = create<ChatState>((set, get) => ({
  currentUser: null,
  users: [],
  conversations: [],
  messages: {},
  activeConversation: null,

  setCurrentUser: (user) => set({ currentUser: user }),

  addUser: (user) => set((state) => ({
    users: [...state.users.filter(u => u.id !== user.id), user]
  })),

  updateUser: (userId, updates) => set((state) => ({
    users: state.users.map(user =>
      user.id === userId ? { ...user, ...updates } : user
    )
  })),

  removeUser: (userId) => set((state) => ({
    users: state.users.filter(user => user.id !== userId)
  })),

  addMessage: (message) => set((state) => {
    const conversationId = [message.fromUser, message.toUser].sort().join('-');
    return {
      messages: {
        ...state.messages,
        [conversationId]: [...(state.messages[conversationId] || []), message]
      }
    };
  }),

  updateMessage: (messageId, updates) => set((state) => {
    const newMessages = { ...state.messages };
    Object.keys(newMessages).forEach(conversationId => {
      newMessages[conversationId] = newMessages[conversationId].map(msg =>
        msg.id === messageId ? { ...msg, ...updates } : msg
      );
    });
    return { messages: newMessages };
  }),

  setActiveConversation: (conversationId) => set({ activeConversation: conversationId }),

  createConversation: (participants) => set((state) => {
    const conversationId = participants.sort().join('-');
    const existingConversation = state.conversations.find(c => c.id === conversationId);

    if (!existingConversation) {
      const newConversation: Conversation = {
        id: conversationId,
        participants,
        unreadCount: 0,
        lastActivity: new Date().toISOString(),
      };

      return {
        conversations: [...state.conversations, newConversation]
      };
    }

    return state;
  }),
}));
```

### 主要React组件

#### 聊天窗口组件
```typescript
// src/components/Chat/ChatWindow.tsx
import React, { useEffect, useRef } from 'react';
import { useChatStore } from '../../store/chatStore';
import { MessageBubble } from './MessageBubble';
import { MessageInput } from './MessageInput';

export const ChatWindow: React.FC = () => {
  const {
    activeConversation,
    messages,
    currentUser
  } = useChatStore();

  const messagesEndRef = useRef<HTMLDivElement>(null);

  const conversationMessages = activeConversation
    ? messages[activeConversation] || []
    : [];

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [conversationMessages]);

  if (!activeConversation) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <p className="text-gray-500">选择一个联系人开始聊天</p>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col">
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {conversationMessages.map((message) => (
          <MessageBubble
            key={message.id}
            message={message}
            isOwn={message.fromUser === currentUser?.id}
          />
        ))}
        <div ref={messagesEndRef} />
      </div>
      <MessageInput />
    </div>
  );
};
```

## 测试策略

### 单元测试
```rust
// src-tauri/src/crypto/tests.rs
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_key_generation() {
        let key_manager = KeyManager::new().unwrap();
        assert!(!key_manager.public_key_der.is_empty());
    }

    #[test]
    fn test_message_encryption_decryption() {
        let alice = KeyManager::new().unwrap();
        let bob = KeyManager::new().unwrap();

        let message = "Hello, Bob!";
        let encrypted = alice.encrypt_message(message, &bob.public_key_der).unwrap();
        let decrypted = bob.decrypt_message(&encrypted).unwrap();

        assert_eq!(message, decrypted);
    }
}
```

### 集成测试
```typescript
// src/tests/integration/chat.test.ts
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ChatWindow } from '../../components/Chat/ChatWindow';
import { useChatStore } from '../../store/chatStore';

describe('ChatWindow Integration', () => {
  beforeEach(() => {
    useChatStore.getState().setCurrentUser({
      id: 'user1',
      displayName: 'Test User',
      publicKey: 'test-key',
      lastSeen: new Date().toISOString(),
      isOnline: true,
    });
  });

  test('should send message when enter is pressed', async () => {
    render(<ChatWindow />);

    const input = screen.getByPlaceholderText('输入消息...');
    fireEvent.change(input, { target: { value: 'Hello World' } });
    fireEvent.keyPress(input, { key: 'Enter', code: 'Enter' });

    await waitFor(() => {
      expect(screen.getByText('Hello World')).toBeInTheDocument();
    });
  });
});
```

## 部署和分发

### 构建配置
```json
// src-tauri/tauri.conf.json
{
  "build": {
    "beforeBuildCommand": "npm run build",
    "beforeDevCommand": "npm run dev",
    "devPath": "http://localhost:3000",
    "distDir": "../dist"
  },
  "package": {
    "productName": "SecureChat P2P",
    "version": "1.0.0"
  },
  "tauri": {
    "allowlist": {
      "all": false,
      "shell": {
        "all": false,
        "open": true
      },
      "dialog": {
        "all": false,
        "open": true,
        "save": true
      },
      "fs": {
        "all": false,
        "readFile": true,
        "writeFile": true,
        "createDir": true
      }
    },
    "bundle": {
      "active": true,
      "targets": "all",
      "identifier": "com.securechat.p2p",
      "icon": [
        "icons/32x32.png",
        "icons/128x128.png",
        "icons/icon.icns",
        "icons/icon.ico"
      ]
    },
    "security": {
      "csp": null
    },
    "windows": [
      {
        "fullscreen": false,
        "height": 700,
        "resizable": true,
        "title": "SecureChat P2P",
        "width": 1000,
        "minWidth": 800,
        "minHeight": 600
      }
    ]
  }
}
```

## 后续扩展

### 可能的功能扩展
- 文件传输
- 语音通话
- 群聊功能
- 移动端支持
- 消息同步
- 主题定制
