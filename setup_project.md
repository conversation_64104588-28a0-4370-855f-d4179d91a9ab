# P2P聊天应用项目初始化指南

## 环境准备

### 1. 安装必需工具

#### Windows环境
```powershell
# 安装 Rust
winget install Rustlang.Rustup

# 安装 Node.js
winget install OpenJS.NodeJS

# 安装 Git
winget install Git.Git

# 重启终端后验证安装
rustc --version
node --version
npm --version
git --version
```

#### 安装Tauri CLI
```powershell
cargo install tauri-cli
```

### 2. 创建项目

#### 初始化Tauri项目
```powershell
# 创建项目目录
mkdir securechat-p2p
cd securechat-p2p

# 初始化Tauri项目
cargo tauri init

# 项目配置选择：
# - App name: SecureChat P2P
# - Window title: SecureChat P2P  
# - Web assets location: ../dist
# - Dev server URL: http://localhost:3000
# - Frontend dev command: npm run dev
# - Frontend build command: npm run build
```

#### 配置前端
```powershell
# 安装前端依赖
npm install react react-dom typescript @types/react @types/react-dom
npm install -D vite @vitejs/plugin-react tailwindcss postcss autoprefixer
npm install @tauri-apps/api zustand react-router-dom lucide-react
npm install -D @types/node eslint prettier @typescript-eslint/eslint-plugin

# 初始化TailwindCSS
npx tailwindcss init -p
```

### 3. 项目结构设置

#### 创建目录结构
```powershell
# 前端目录
mkdir src\components\Chat
mkdir src\components\ContactList  
mkdir src\components\Discovery
mkdir src\components\Settings
mkdir src\hooks
mkdir src\store
mkdir src\types
mkdir src\utils
mkdir src\styles

# 后端目录
mkdir src-tauri\src\commands
mkdir src-tauri\src\network
mkdir src-tauri\src\crypto
mkdir src-tauri\src\storage
mkdir src-tauri\src\models
```

#### 配置文件

**package.json**
```json
{
  "name": "securechat-p2p",
  "private": true,
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "tauri": "tauri",
    "tauri:dev": "tauri dev",
    "tauri:build": "tauri build",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\""
  },
  "dependencies": {
    "@tauri-apps/api": "^1.5.0",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.15.0",
    "zustand": "^4.4.0",
    "lucide-react": "^0.263.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.15",
    "@types/react-dom": "^18.2.7",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "@vitejs/plugin-react": "^4.0.3",
    "autoprefixer": "^10.4.14",
    "eslint": "^8.45.0",
    "eslint-plugin-react-hooks": "^4.6.0",
    "eslint-plugin-react-refresh": "^0.4.3",
    "postcss": "^8.4.27",
    "prettier": "^3.0.0",
    "tailwindcss": "^3.3.0",
    "typescript": "^5.0.2",
    "vite": "^4.4.5"
  }
}
```

**tailwind.config.js**
```javascript
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
        },
        gray: {
          50: '#f9fafb',
          100: '#f3f4f6',
          200: '#e5e7eb',
          300: '#d1d5db',
          400: '#9ca3af',
          500: '#6b7280',
          600: '#4b5563',
          700: '#374151',
          800: '#1f2937',
          900: '#111827',
        }
      }
    },
  },
  plugins: [],
}
```

**vite.config.ts**
```typescript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  clearScreen: false,
  server: {
    port: 3000,
    strictPort: true,
  },
  envPrefix: ['VITE_', 'TAURI_'],
  build: {
    target: process.env.TAURI_PLATFORM == 'windows' ? 'chrome105' : 'safari13',
    minify: !process.env.TAURI_DEBUG ? 'esbuild' : false,
    sourcemap: !!process.env.TAURI_DEBUG,
  },
})
```

**src-tauri/Cargo.toml**
```toml
[package]
name = "securechat-p2p"
version = "1.0.0"
description = "A secure P2P chat application"
authors = ["Your Name"]
license = "MIT"
repository = ""
edition = "2021"

[build-dependencies]
tauri-build = { version = "1.0", features = [] }

[dependencies]
tauri = { version = "1.0", features = ["api-all"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
uuid = { version = "1.0", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }
sqlx = { version = "0.7", features = ["sqlite", "runtime-tokio-rustls", "chrono", "uuid"] }
ring = "0.16"
base64 = "0.21"
mdns = "3.0"
local-ip-address = "0.5"

[features]
default = ["custom-protocol"]
custom-protocol = ["tauri/custom-protocol"]
```

### 4. 基础代码模板

#### TypeScript类型定义
```typescript
// src/types/index.ts
export interface User {
  id: string;
  displayName: string;
  publicKey: string;
  ipAddress?: string;
  port?: number;
  lastSeen: string;
  isOnline: boolean;
}

export interface Message {
  id: string;
  fromUser: string;
  toUser: string;
  content: string;
  timestamp: string;
  messageType: 'text' | 'file' | 'system';
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  encrypted: boolean;
}

export interface Conversation {
  id: string;
  participants: [string, string];
  lastMessage?: Message;
  unreadCount: number;
  lastActivity: string;
}

export interface NetworkConfig {
  discoveryPort: number;
  messagePort: number;
  serverUrl?: string;
  enableLanDiscovery: boolean;
  enableServerDiscovery: boolean;
}
```

#### Rust基础结构
```rust
// src-tauri/src/models/mod.rs
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct User {
    pub id: String,
    pub display_name: String,
    pub public_key: String,
    pub ip_address: Option<String>,
    pub port: Option<u16>,
    pub last_seen: DateTime<Utc>,
    pub is_online: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Message {
    pub id: String,
    pub from_user: String,
    pub to_user: String,
    pub content: String,
    pub timestamp: DateTime<Utc>,
    pub message_type: MessageType,
    pub status: MessageStatus,
    pub encrypted: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MessageType {
    Text,
    File,
    System,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MessageStatus {
    Sending,
    Sent,
    Delivered,
    Read,
    Failed,
}
```

### 5. 开发工作流

#### 日常开发命令
```powershell
# 启动开发服务器
npm run tauri:dev

# 代码格式化
npm run format

# 代码检查
npm run lint

# 构建应用
npm run tauri:build

# 运行测试
cargo test
```

#### Git工作流
```powershell
# 初始化Git仓库
git init
git add .
git commit -m "Initial project setup"

# 创建开发分支
git checkout -b develop

# 功能分支开发
git checkout -b feature/lan-discovery
# ... 开发完成后
git checkout develop
git merge feature/lan-discovery
```

### 6. 调试配置

#### VS Code配置
创建 `.vscode/launch.json`:
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "lldb",
      "request": "launch",
      "name": "Tauri Development Debug",
      "cargo": {
        "args": [
          "build",
          "--manifest-path=./src-tauri/Cargo.toml",
          "--no-default-features"
        ],
        "filter": {
          "name": "securechat-p2p",
          "kind": "bin"
        }
      },
      "args": [],
      "cwd": "${workspaceFolder}"
    }
  ]
}
```

### 7. 项目检查清单

#### 环境验证
- [ ] Rust工具链安装完成
- [ ] Node.js和npm可用
- [ ] Tauri CLI安装成功
- [ ] 项目目录结构正确

#### 基础功能验证
- [ ] `npm run tauri:dev` 能正常启动
- [ ] 应用窗口正常显示
- [ ] 前后端通信正常
- [ ] 热重载功能工作

#### 开发工具配置
- [ ] ESLint配置正确
- [ ] Prettier格式化正常
- [ ] TypeScript类型检查通过
- [ ] Git仓库初始化完成

## 下一步

完成项目初始化后，可以开始第一阶段的开发：

1. 实现基础UI组件
2. 配置状态管理
3. 建立前后端通信
4. 实现基础的用户界面

参考详细的开发计划文档继续后续开发工作。
