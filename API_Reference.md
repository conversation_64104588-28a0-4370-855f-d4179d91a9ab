# P2P聊天应用 API 参考文档

## Tauri Commands API

### 用户管理

#### `get_current_user`
获取当前用户信息
```rust
#[tauri::command]
async fn get_current_user() -> Result<Option<User>, String>
```

**返回值**:
```typescript
User | null
```

#### `create_user`
创建新用户
```rust
#[tauri::command]
async fn create_user(display_name: String) -> Result<User, String>
```

**参数**:
- `display_name`: 用户显示名称

**返回值**:
```typescript
User
```

#### `update_user`
更新用户信息
```rust
#[tauri::command]
async fn update_user(user_id: String, display_name: Option<String>) -> Result<User, String>
```

### 消息管理

#### `send_message`
发送消息
```rust
#[tauri::command]
async fn send_message(to_user: String, content: String) -> Result<Message, String>
```

**参数**:
- `to_user`: 接收用户ID
- `content`: 消息内容

**返回值**:
```typescript
Message
```

#### `get_messages`
获取会话消息
```rust
#[tauri::command]
async fn get_messages(conversation_id: String, limit: Option<u32>, offset: Option<u32>) -> Result<Vec<Message>, String>
```

**参数**:
- `conversation_id`: 会话ID
- `limit`: 消息数量限制 (默认50)
- `offset`: 偏移量 (默认0)

**返回值**:
```typescript
Message[]
```

#### `mark_messages_read`
标记消息为已读
```rust
#[tauri::command]
async fn mark_messages_read(conversation_id: String) -> Result<(), String>
```

### 网络发现

#### `start_lan_discovery`
启动局域网发现
```rust
#[tauri::command]
async fn start_lan_discovery() -> Result<(), String>
```

#### `stop_lan_discovery`
停止局域网发现
```rust
#[tauri::command]
async fn stop_lan_discovery() -> Result<(), String>
```

#### `get_discovered_users`
获取发现的用户列表
```rust
#[tauri::command]
async fn get_discovered_users() -> Result<Vec<User>, String>
```

**返回值**:
```typescript
User[]
```

#### `connect_to_user`
连接到指定用户
```rust
#[tauri::command]
async fn connect_to_user(user_id: String) -> Result<(), String>
```

**参数**:
- `user_id`: 目标用户ID

#### `disconnect_from_user`
断开与用户的连接
```rust
#[tauri::command]
async fn disconnect_from_user(user_id: String) -> Result<(), String>
```

### 加密管理

#### `generate_keypair`
生成密钥对
```rust
#[tauri::command]
async fn generate_keypair() -> Result<KeyPair, String>
```

**返回值**:
```typescript
{
  publicKey: string;
  privateKey: string; // 仅用于内部存储，不暴露给前端
}
```

#### `get_public_key`
获取公钥
```rust
#[tauri::command]
async fn get_public_key() -> Result<String, String>
```

#### `verify_user_key`
验证用户公钥
```rust
#[tauri::command]
async fn verify_user_key(user_id: String, public_key: String) -> Result<bool, String>
```

### 配置管理

#### `get_config`
获取应用配置
```rust
#[tauri::command]
async fn get_config() -> Result<AppConfig, String>
```

**返回值**:
```typescript
{
  discoveryPort: number;
  messagePort: number;
  serverUrl?: string;
  enableLanDiscovery: boolean;
  enableServerDiscovery: boolean;
  autoConnect: boolean;
  encryptionEnabled: boolean;
}
```

#### `update_config`
更新应用配置
```rust
#[tauri::command]
async fn update_config(config: AppConfig) -> Result<(), String>
```

## 事件系统

### 前端监听事件

#### `user_discovered`
发现新用户
```typescript
import { listen } from '@tauri-apps/api/event';

listen<User>('user_discovered', (event) => {
  console.log('发现新用户:', event.payload);
});
```

#### `user_connected`
用户连接成功
```typescript
listen<{ userId: string }>('user_connected', (event) => {
  console.log('用户连接:', event.payload.userId);
});
```

#### `user_disconnected`
用户断开连接
```typescript
listen<{ userId: string }>('user_disconnected', (event) => {
  console.log('用户断开:', event.payload.userId);
});
```

#### `message_received`
接收到新消息
```typescript
listen<Message>('message_received', (event) => {
  console.log('新消息:', event.payload);
});
```

#### `message_status_updated`
消息状态更新
```typescript
listen<{ messageId: string; status: MessageStatus }>('message_status_updated', (event) => {
  console.log('消息状态更新:', event.payload);
});
```

#### `connection_error`
连接错误
```typescript
listen<{ error: string; userId?: string }>('connection_error', (event) => {
  console.log('连接错误:', event.payload);
});
```

## 前端API封装

### 用户API
```typescript
// src/api/users.ts
import { invoke } from '@tauri-apps/api/tauri';
import { User } from '../types';

export const userApi = {
  getCurrentUser: (): Promise<User | null> => 
    invoke('get_current_user'),
    
  createUser: (displayName: string): Promise<User> => 
    invoke('create_user', { displayName }),
    
  updateUser: (userId: string, displayName?: string): Promise<User> => 
    invoke('update_user', { userId, displayName }),
};
```

### 消息API
```typescript
// src/api/messages.ts
import { invoke } from '@tauri-apps/api/tauri';
import { Message } from '../types';

export const messageApi = {
  sendMessage: (toUser: string, content: string): Promise<Message> => 
    invoke('send_message', { toUser, content }),
    
  getMessages: (conversationId: string, limit = 50, offset = 0): Promise<Message[]> => 
    invoke('get_messages', { conversationId, limit, offset }),
    
  markMessagesRead: (conversationId: string): Promise<void> => 
    invoke('mark_messages_read', { conversationId }),
};
```

### 网络API
```typescript
// src/api/network.ts
import { invoke } from '@tauri-apps/api/tauri';
import { User } from '../types';

export const networkApi = {
  startLanDiscovery: (): Promise<void> => 
    invoke('start_lan_discovery'),
    
  stopLanDiscovery: (): Promise<void> => 
    invoke('stop_lan_discovery'),
    
  getDiscoveredUsers: (): Promise<User[]> => 
    invoke('get_discovered_users'),
    
  connectToUser: (userId: string): Promise<void> => 
    invoke('connect_to_user', { userId }),
    
  disconnectFromUser: (userId: string): Promise<void> => 
    invoke('disconnect_from_user', { userId }),
};
```

## 错误处理

### 错误类型定义
```typescript
// src/types/errors.ts
export interface ApiError {
  code: string;
  message: string;
  details?: any;
}

export enum ErrorCode {
  NETWORK_ERROR = 'NETWORK_ERROR',
  ENCRYPTION_ERROR = 'ENCRYPTION_ERROR',
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  CONNECTION_FAILED = 'CONNECTION_FAILED',
  INVALID_MESSAGE = 'INVALID_MESSAGE',
  DATABASE_ERROR = 'DATABASE_ERROR',
}
```

### 错误处理工具
```typescript
// src/utils/errorHandler.ts
import { ApiError, ErrorCode } from '../types/errors';

export const handleApiError = (error: unknown): ApiError => {
  if (typeof error === 'string') {
    try {
      return JSON.parse(error) as ApiError;
    } catch {
      return {
        code: ErrorCode.NETWORK_ERROR,
        message: error,
      };
    }
  }
  
  return {
    code: ErrorCode.NETWORK_ERROR,
    message: '未知错误',
  };
};

export const showErrorNotification = (error: ApiError) => {
  // 显示错误通知的逻辑
  console.error(`[${error.code}] ${error.message}`);
};
```

## 使用示例

### 发送消息完整流程
```typescript
// src/hooks/useChat.ts
import { useState } from 'react';
import { messageApi } from '../api/messages';
import { useChatStore } from '../store/chatStore';
import { handleApiError, showErrorNotification } from '../utils/errorHandler';

export const useChat = () => {
  const [sending, setSending] = useState(false);
  const { addMessage, updateMessage } = useChatStore();
  
  const sendMessage = async (toUser: string, content: string) => {
    setSending(true);
    
    try {
      const message = await messageApi.sendMessage(toUser, content);
      addMessage(message);
    } catch (error) {
      const apiError = handleApiError(error);
      showErrorNotification(apiError);
    } finally {
      setSending(false);
    }
  };
  
  return {
    sendMessage,
    sending,
  };
};
```

### 用户发现和连接
```typescript
// src/hooks/useDiscovery.ts
import { useEffect, useState } from 'react';
import { listen } from '@tauri-apps/api/event';
import { networkApi } from '../api/network';
import { User } from '../types';

export const useDiscovery = () => {
  const [discoveredUsers, setDiscoveredUsers] = useState<User[]>([]);
  const [discovering, setDiscovering] = useState(false);
  
  useEffect(() => {
    const unlistenUserDiscovered = listen<User>('user_discovered', (event) => {
      setDiscoveredUsers(prev => {
        const existing = prev.find(u => u.id === event.payload.id);
        if (existing) {
          return prev.map(u => u.id === event.payload.id ? event.payload : u);
        }
        return [...prev, event.payload];
      });
    });
    
    return () => {
      unlistenUserDiscovered.then(fn => fn());
    };
  }, []);
  
  const startDiscovery = async () => {
    setDiscovering(true);
    try {
      await networkApi.startLanDiscovery();
      const users = await networkApi.getDiscoveredUsers();
      setDiscoveredUsers(users);
    } catch (error) {
      console.error('启动发现失败:', error);
    } finally {
      setDiscovering(false);
    }
  };
  
  const stopDiscovery = async () => {
    try {
      await networkApi.stopLanDiscovery();
      setDiscoveredUsers([]);
    } catch (error) {
      console.error('停止发现失败:', error);
    }
  };
  
  return {
    discoveredUsers,
    discovering,
    startDiscovery,
    stopDiscovery,
  };
};
```
